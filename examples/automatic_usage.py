# SPDX-FileCopyrightText: Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import numpy as np
import matplotlib.pyplot as plt
import PIL.Image
import argparse
import os
from nanosam.utils.predictor import AutomaticPredictor


def show_anns(anns, ax):
    """Display annotations on the given axis."""
    if len(anns) == 0:
        return
    sorted_anns = sorted(anns, key=(lambda x: x['area']), reverse=True)
    
    for ann in sorted_anns:
        m = ann['segmentation']
        img = np.ones((m.shape[0], m.shape[1], 3))
        color_mask = np.random.random((1, 3)).tolist()[0]
        for i in range(3):
            img[:,:,i] = color_mask[i]
        ax.imshow(np.dstack((img, m*0.35)))


def main():
    parser = argparse.ArgumentParser(description="Automatic mask generation using TensorRT NanoSAM")
    parser.add_argument("--image_encoder", type=str, default="data/resnet18_image_encoder.engine",
                       help="Path to TensorRT image encoder engine")
    parser.add_argument("--mask_decoder", type=str, default="data/mobile_sam_mask_decoder.engine",
                       help="Path to TensorRT mask decoder engine")
    parser.add_argument("--input_image", type=str, default="assets/dogs.jpg",
                       help="Path to input image")
    parser.add_argument("--output_dir", type=str, default="data",
                       help="Directory to save output images")
    parser.add_argument("--points_per_side", type=int, default=16,
                       help="Number of points per side for grid generation")
    parser.add_argument("--pred_iou_thresh", type=float, default=0.88,
                       help="IoU threshold for filtering predictions")
    parser.add_argument("--stability_score_thresh", type=float, default=0.95,
                       help="Stability score threshold for filtering")
    parser.add_argument("--box_nms_thresh", type=float, default=0.7,
                       help="NMS threshold for removing duplicate boxes")
    
    args = parser.parse_args()
    
    # Check if input image exists
    if not os.path.exists(args.input_image):
        print(f"Error: Input image {args.input_image} not found!")
        return
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("Initializing TensorRT AutomaticPredictor...")
    
    # Initialize the automatic predictor
    predictor = AutomaticPredictor(
        image_encoder_engine=args.image_encoder,
        mask_decoder_engine=args.mask_decoder,
        points_per_side=args.points_per_side,
        pred_iou_thresh=args.pred_iou_thresh,
        stability_score_thresh=args.stability_score_thresh,
        box_nms_thresh=args.box_nms_thresh,
    )
    
    print(f"Loading image: {args.input_image}")
    
    # Load and process image
    image = PIL.Image.open(args.input_image).convert("RGB")
    
    print("Generating masks automatically...")
    
    # Generate masks automatically
    masks = predictor.generate_masks(image)
    
    print(f"Generated {len(masks)} masks")
    
    # Create visualization
    fig, axes = plt.subplots(1, 2, figsize=(20, 10))
    
    # Show original image
    axes[0].imshow(image)
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # Show image with masks
    axes[1].imshow(image)
    show_anns(masks, axes[1])
    axes[1].set_title(f'Automatic Segmentation ({len(masks)} masks)')
    axes[1].axis('off')
    
    # Save result
    output_path = os.path.join(args.output_dir, "automatic_usage_out.jpg")
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    print(f"Result saved to: {output_path}")
    
    # Print statistics
    if masks:
        areas = [mask['area'] for mask in masks]
        ious = [mask['predicted_iou'] for mask in masks]
        stability_scores = [mask['stability_score'] for mask in masks]
        
        print(f"\nMask Statistics:")
        print(f"  Number of masks: {len(masks)}")
        print(f"  Average area: {np.mean(areas):.1f} pixels")
        print(f"  Average IoU: {np.mean(ious):.3f}")
        print(f"  Average stability score: {np.mean(stability_scores):.3f}")
        print(f"  Area range: {np.min(areas):.0f} - {np.max(areas):.0f} pixels")
    
    print("Done!")


if __name__ == "__main__":
    main()
