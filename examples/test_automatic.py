#!/usr/bin/env python3
"""
Simple test script for AutomaticPredictor functionality.
This script tests the basic functionality without requiring actual TensorRT engines.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import PIL.Image

def test_automatic_predictor_import():
    """Test if AutomaticPredictor can be imported successfully."""
    try:
        from nanosam.utils.predictor import AutomaticPredictor
        print("✓ AutomaticPredictor imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import AutomaticPredictor: {e}")
        return False

def test_automatic_predictor_init():
    """Test if AutomaticPredictor can be initialized (without actual engines)."""
    try:
        from nanosam.utils.predictor import AutomaticPredictor
        
        # This will fail because engines don't exist, but we can test the parameter handling
        try:
            predictor = AutomaticPredictor(
                image_encoder_engine="dummy_encoder.engine",
                mask_decoder_engine="dummy_decoder.engine",
                points_per_side=16,
                pred_iou_thresh=0.88,
                stability_score_thresh=0.95,
                box_nms_thresh=0.7,
            )
        except Exception as e:
            if "No such file or directory" in str(e) or "FileNotFoundError" in str(e):
                print("✓ AutomaticPredictor initialization parameters handled correctly")
                print("  (Expected failure due to missing engine files)")
                return True
            else:
                print(f"✗ Unexpected error during initialization: {e}")
                return False
        
        print("✗ Expected initialization to fail due to missing engines")
        return False
        
    except Exception as e:
        print(f"✗ Failed to test AutomaticPredictor initialization: {e}")
        return False

def test_utility_functions():
    """Test if utility functions are accessible."""
    try:
        from nanosam.utils.predictor import preprocess_image, preprocess_points
        
        # Test preprocess_image with dummy data
        dummy_image = PIL.Image.new('RGB', (100, 100), color='red')
        try:
            # This will fail without CUDA, but we can test the function exists
            result = preprocess_image(dummy_image, 512)
        except Exception as e:
            if "CUDA" in str(e) or "cuda" in str(e):
                print("✓ preprocess_image function accessible (CUDA not available)")
            else:
                print(f"✓ preprocess_image function accessible (error: {e})")
        
        # Test preprocess_points
        dummy_points = np.array([[50, 50], [75, 75]])
        result = preprocess_points(dummy_points, (100, 100), 1024)
        expected_scale = 1024 / 100
        expected_result = dummy_points * expected_scale
        
        if np.allclose(result, expected_result):
            print("✓ preprocess_points function works correctly")
        else:
            print(f"✗ preprocess_points function incorrect: {result} vs {expected_result}")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Failed to test utility functions: {e}")
        return False

def test_amg_utilities():
    """Test if AMG utility functions are accessible."""
    try:
        from nanosam.mobile_sam.utils.amg import (
            build_all_layer_point_grids,
            batch_iterator,
            MaskData,
            build_point_grid
        )
        
        # Test point grid generation
        points = build_point_grid(4)
        if points.shape == (16, 2) and np.all(points >= 0) and np.all(points <= 1):
            print("✓ build_point_grid function works correctly")
        else:
            print(f"✗ build_point_grid function incorrect: shape {points.shape}")
            return False
        
        # Test multi-layer point grids
        point_grids = build_all_layer_point_grids(4, 1, 2)
        if len(point_grids) == 2:  # n_layers + 1
            print("✓ build_all_layer_point_grids function works correctly")
        else:
            print(f"✗ build_all_layer_point_grids function incorrect: {len(point_grids)} layers")
            return False
        
        # Test batch iterator
        data = list(range(10))
        batches = list(batch_iterator(3, data))
        if len(batches) == 4 and len(batches[0][0]) == 3 and len(batches[-1][0]) == 1:
            print("✓ batch_iterator function works correctly")
        else:
            print(f"✗ batch_iterator function incorrect: {len(batches)} batches")
            return False
        
        # Test MaskData
        mask_data = MaskData(test_data=[1, 2, 3])
        if mask_data["test_data"] == [1, 2, 3]:
            print("✓ MaskData class works correctly")
        else:
            print("✗ MaskData class incorrect")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Failed to test AMG utilities: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing AutomaticPredictor implementation...")
    print("=" * 50)
    
    tests = [
        test_automatic_predictor_import,
        test_automatic_predictor_init,
        test_utility_functions,
        test_amg_utilities,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! AutomaticPredictor implementation looks good.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
