# SPDX-FileCopyrightText: Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from torch2trt import TRTModule
from typing import Tuple
import tensorrt as trt
import PIL.Image
import torch
import numpy as np
import torch.nn.functional as F

def load_mask_decoder_engine(path: str):
    
    with trt.Logger() as logger, trt.Runtime(logger) as runtime:
        with open(path, 'rb') as f:
            engine_bytes = f.read()
        engine = runtime.deserialize_cuda_engine(engine_bytes)

    mask_decoder_trt = TRTModule(
        engine=engine,
        input_names=[
            "image_embeddings",
            "point_coords",
            "point_labels",
            "mask_input",
            "has_mask_input"
        ],
        output_names=[
            "iou_predictions",
            "low_res_masks"
        ]
    )

    return mask_decoder_trt


def load_image_encoder_engine(path: str):

    with trt.Logger() as logger, trt.Runtime(logger) as runtime:
        with open(path, 'rb') as f:
            engine_bytes = f.read()
        engine = runtime.deserialize_cuda_engine(engine_bytes)

    image_encoder_trt = TRTModule(
        engine=engine,
        input_names=["image"],
        output_names=["image_embeddings"]
    )

    return image_encoder_trt


def preprocess_image(image, size: int = 512):

    if isinstance(image, np.ndarray):
        image = PIL.Image.fromarray(image)

    image_mean = torch.tensor([123.675, 116.28, 103.53])[:, None, None]
    image_std = torch.tensor([58.395, 57.12, 57.375])[:, None, None]

    image_pil = image
    aspect_ratio = image_pil.width / image_pil.height
    if aspect_ratio >= 1:
        resize_width = size
        resize_height = int(size / aspect_ratio)
    else:
        resize_height = size
        resize_width = int(size * aspect_ratio)

    image_pil_resized = image_pil.resize((resize_width, resize_height))
    image_np_resized = np.asarray(image_pil_resized)
    # to writable
    image_np_resized = image_np_resized.copy()
    image_torch_resized = torch.from_numpy(image_np_resized).permute(2, 0, 1)
    image_torch_resized_normalized = (image_torch_resized.float() - image_mean) / image_std
    image_tensor = torch.zeros((1, 3, size, size))
    image_tensor[0, :, :resize_height, :resize_width] = image_torch_resized_normalized

    return image_tensor.cuda()


def preprocess_points(points, image_size, size: int = 1024):
    scale = size / max(*image_size)
    points = points * scale
    return points


def run_mask_decoder(mask_decoder_engine, features, points=None, point_labels=None, mask_input=None):
    if points is not None:
        assert point_labels is not None
        assert len(points) == len(point_labels)
    
    points = np.array([points])  
    image_point_coords = torch.from_numpy(points).float().cuda()
    point_labels = np.array([point_labels]) 
    image_point_labels = torch.from_numpy(point_labels).float().cuda()
    
    # image_point_coords = torch.tensor([points]).float().cuda()
    # image_point_labels = torch.tensor([point_labels]).float().cuda()

    if mask_input is None:
        mask_input = torch.zeros(1, 1, 256, 256).float().cuda()
        has_mask_input = torch.tensor([0]).float().cuda()
    else:
        has_mask_input = torch.tensor([1]).float().cuda()


    iou_predictions, low_res_masks = mask_decoder_engine(
        features,
        image_point_coords,
        image_point_labels,
        mask_input,
        has_mask_input
    )

    return iou_predictions, low_res_masks


def upscale_mask(mask, image_shape, size=256):
    
    if image_shape[1] > image_shape[0]:
        lim_x = size
        lim_y = int(size * image_shape[0] / image_shape[1])
    else:
        lim_x = int(size * image_shape[1] / image_shape[0])
        lim_y = size

    mask[:, :, :lim_y, :lim_x]
    mask = F.interpolate(mask[:, :, :lim_y, :lim_x], image_shape, mode='bilinear')
    
    return mask


class Predictor(object):

    def __init__(self,
            image_encoder_engine: str,
            mask_decoder_engine: str,
            image_encoder_size: int = 1024,
            orig_image_encoder_size: int = 1024,
        ):
        self.image_encoder_engine = load_image_encoder_engine(image_encoder_engine)
        self.mask_decoder_engine = load_mask_decoder_engine(mask_decoder_engine)
        self.image_encoder_size = image_encoder_size
        self.orig_image_encoder_size = orig_image_encoder_size

    def set_image(self, image):
        self.image = image
        self.image_tensor = preprocess_image(image, self.image_encoder_size)
        self.features = self.image_encoder_engine(self.image_tensor)

    def predict(self, points, point_labels, mask_input=None):
        points = preprocess_points(
            points, 
            (self.image.height, self.image.width),
            self.orig_image_encoder_size
        )
        mask_iou, low_res_mask = run_mask_decoder(
            self.mask_decoder_engine,
            self.features,
            points,
            point_labels,
            mask_input
        )

        hi_res_mask = upscale_mask(
            low_res_mask, 
            (self.image.height, self.image.width)                           
        )

        return hi_res_mask, mask_iou, low_res_mask


class AutomaticPredictor(object):
    """
    TensorRT-based automatic mask generator that generates masks for an entire image
    without requiring manual prompts.
    """

    def __init__(self,
            image_encoder_engine: str,
            mask_decoder_engine: str,
            image_encoder_size: int = 1024,
            orig_image_encoder_size: int = 1024,
            points_per_side: int = 32,
            points_per_batch: int = 64,
            pred_iou_thresh: float = 0.88,
            stability_score_thresh: float = 0.95,
            box_nms_thresh: float = 0.7,
            crop_n_layers: int = 0,
            crop_overlap_ratio: float = 512 / 1500,
            min_mask_region_area: int = 0,
        ):
        """
        Initialize the AutomaticPredictor with TensorRT engines.

        Args:
            image_encoder_engine: Path to TensorRT image encoder engine
            mask_decoder_engine: Path to TensorRT mask decoder engine
            image_encoder_size: Input size for image encoder
            orig_image_encoder_size: Original image encoder size for coordinate scaling
            points_per_side: Number of points per side for point grid generation
            points_per_batch: Number of points to process in each batch
            pred_iou_thresh: IoU threshold for filtering predictions
            stability_score_thresh: Stability score threshold for filtering
            box_nms_thresh: NMS threshold for removing duplicate boxes
            crop_n_layers: Number of crop layers (0 means no cropping)
            crop_overlap_ratio: Overlap ratio between crops
            min_mask_region_area: Minimum area for mask regions
        """
        # Initialize TensorRT engines
        self.image_encoder_engine = load_image_encoder_engine(image_encoder_engine)
        self.mask_decoder_engine = load_mask_decoder_engine(mask_decoder_engine)
        self.image_encoder_size = image_encoder_size
        self.orig_image_encoder_size = orig_image_encoder_size

        # Automatic mask generation parameters
        self.points_per_side = points_per_side
        self.points_per_batch = points_per_batch
        self.pred_iou_thresh = pred_iou_thresh
        self.stability_score_thresh = stability_score_thresh
        self.box_nms_thresh = box_nms_thresh
        self.crop_n_layers = crop_n_layers
        self.crop_overlap_ratio = crop_overlap_ratio
        self.min_mask_region_area = min_mask_region_area

        # Import required utilities
        from ..mobile_sam.utils.amg import (
            build_all_layer_point_grids,
            batch_iterator,
            MaskData,
            generate_crop_boxes,
            batched_mask_to_box,
            mask_to_rle_pytorch,
            calculate_stability_score,
            remove_small_regions,
            rle_to_mask
        )

        self.build_all_layer_point_grids = build_all_layer_point_grids
        self.batch_iterator = batch_iterator
        self.MaskData = MaskData
        self.generate_crop_boxes = generate_crop_boxes
        self.batched_mask_to_box = batched_mask_to_box
        self.mask_to_rle_pytorch = mask_to_rle_pytorch
        self.calculate_stability_score = calculate_stability_score
        self.remove_small_regions = remove_small_regions
        self.rle_to_mask = rle_to_mask

        # Generate point grids
        self.point_grids = self.build_all_layer_point_grids(
            points_per_side,
            crop_n_layers,
            1  # crop_n_points_downscale_factor
        )

    def generate_masks(self, image):
        """
        Generate masks for the entire image automatically.

        Args:
            image: PIL Image or numpy array in HWC format

        Returns:
            List of dictionaries containing mask data
        """
        # Convert PIL image to numpy if needed
        if hasattr(image, 'convert'):
            image_np = np.array(image.convert('RGB'))
        else:
            image_np = image

        # Set image for feature extraction
        self.image = image if hasattr(image, 'height') else PIL.Image.fromarray(image_np)
        self.image_tensor = preprocess_image(self.image, self.image_encoder_size)
        self.features = self.image_encoder_engine(self.image_tensor)

        # Generate masks
        mask_data = self._generate_masks(image_np)

        # Convert to output format
        results = []
        for i in range(len(mask_data["rles"])):
            mask = self.rle_to_mask(mask_data["rles"][i])
            result = {
                "segmentation": mask,
                "area": int(mask.sum()),
                "bbox": mask_data["boxes"][i].tolist(),
                "predicted_iou": float(mask_data["iou_preds"][i]),
                "point_coords": mask_data["points"][i].tolist(),
                "stability_score": float(mask_data["stability_scores"][i]),
                "crop_box": mask_data["crop_boxes"][i].tolist(),
            }
            results.append(result)

        return results

    def _generate_masks(self, image):
        """Generate masks using point grid sampling."""
        orig_size = image.shape[:2]
        crop_boxes, layer_idxs = self.generate_crop_boxes(
            orig_size, self.crop_n_layers, self.crop_overlap_ratio
        )

        # Process each crop
        data = self.MaskData()
        for crop_box, layer_idx in zip(crop_boxes, layer_idxs):
            crop_data = self._process_crop(image, crop_box, layer_idx)
            data.cat(crop_data)

        # Remove duplicates if multiple crops
        if len(crop_boxes) > 1:
            from torchvision.ops.boxes import batched_nms, box_area
            scores = 1 / box_area(data["crop_boxes"])
            scores = scores.to(data["boxes"].device)
            keep_by_nms = batched_nms(
                data["boxes"].float(),
                scores,
                torch.zeros_like(data["boxes"][:, 0]),
                iou_threshold=self.box_nms_thresh,
            )
            data.filter(keep_by_nms)

        data.to_numpy()
        return data

    def _process_crop(self, image, crop_box, crop_layer_idx):
        """Process a single crop of the image."""
        # Crop the image
        x0, y0, x1, y1 = crop_box
        cropped_im = image[y0:y1, x0:x1, :]
        cropped_im_size = cropped_im.shape[:2]

        # Set cropped image for feature extraction
        cropped_pil = PIL.Image.fromarray(cropped_im)
        cropped_tensor = preprocess_image(cropped_pil, self.image_encoder_size)
        cropped_features = self.image_encoder_engine(cropped_tensor)

        # Get points for this crop
        points_scale = np.array(cropped_im_size)[None, ::-1]
        points_for_image = self.point_grids[crop_layer_idx] * points_scale

        # Generate masks in batches
        data = self.MaskData()
        for (points,) in self.batch_iterator(self.points_per_batch, points_for_image):
            batch_data = self._process_batch(points, cropped_im_size, cropped_features)
            data.cat(batch_data)

        # Remove duplicates within this crop
        from torchvision.ops.boxes import batched_nms
        keep_by_nms = batched_nms(
            data["boxes"].float(),
            data["iou_preds"],
            torch.zeros_like(data["boxes"][:, 0]),
            iou_threshold=self.box_nms_thresh,
        )
        data.filter(keep_by_nms)

        # Return to original image coordinates
        from ..mobile_sam.utils.amg import uncrop_boxes_xyxy, uncrop_points
        data["boxes"] = uncrop_boxes_xyxy(data["boxes"], crop_box)
        data["points"] = uncrop_points(data["points"], crop_box)
        data["crop_boxes"] = torch.tensor([crop_box for _ in range(len(data["rles"]))])

        return data

    def _process_batch(self, points, im_size, features):
        """Process a batch of points to generate masks."""
        # Preprocess points for TensorRT model
        points_processed = preprocess_points(
            points,
            im_size,
            self.orig_image_encoder_size
        )

        # Generate masks for each point
        masks_list = []
        iou_preds_list = []
        points_list = []

        for point in points_processed:
            # Run TensorRT mask decoder for single point
            point_labels = np.array([1])  # Foreground point
            mask_iou, low_res_mask = run_mask_decoder(
                self.mask_decoder_engine,
                features,
                [point],
                point_labels,
                None
            )

            # Upscale mask
            hi_res_mask = upscale_mask(
                low_res_mask,
                im_size
            )

            # Store results (take best mask based on IoU)
            best_idx = int(mask_iou.argmax())
            masks_list.append(hi_res_mask[:, best_idx:best_idx+1, :, :])
            iou_preds_list.append(mask_iou[:, best_idx])
            points_list.append(point)

        # Stack results
        masks = torch.cat(masks_list, dim=1)  # [1, N, H, W]
        iou_preds = torch.cat(iou_preds_list, dim=0)  # [N]
        points_tensor = torch.tensor(np.array(points_list))  # [N, 2]

        # Filter by IoU threshold
        keep_mask = iou_preds > self.pred_iou_thresh
        masks = masks[:, keep_mask, :, :]
        iou_preds = iou_preds[keep_mask]
        points_tensor = points_tensor[keep_mask]

        if masks.shape[1] == 0:
            # No masks passed threshold
            return self.MaskData()

        # Calculate stability scores
        stability_scores = self.calculate_stability_score(
            masks[0], 0.0, 1.0
        )

        # Filter by stability score
        keep_mask = stability_scores > self.stability_score_thresh
        masks = masks[:, keep_mask, :, :]
        iou_preds = iou_preds[keep_mask]
        points_tensor = points_tensor[keep_mask]
        stability_scores = stability_scores[keep_mask]

        if masks.shape[1] == 0:
            # No masks passed stability threshold
            return self.MaskData()

        # Convert masks to RLE and calculate boxes
        masks_np = masks[0].detach().cpu().numpy()  # [N, H, W]
        rles = []
        boxes = []

        for i in range(masks_np.shape[0]):
            mask = masks_np[i] > 0
            rle = self.mask_to_rle_pytorch(torch.tensor(mask).unsqueeze(0))[0]
            rles.append(rle)

            # Calculate bounding box
            if mask.any():
                y_indices, x_indices = np.where(mask)
                x_min, x_max = x_indices.min(), x_indices.max()
                y_min, y_max = y_indices.min(), y_indices.max()
                boxes.append([x_min, y_min, x_max, y_max])
            else:
                boxes.append([0, 0, 0, 0])

        # Create MaskData
        data = self.MaskData(
            rles=rles,
            boxes=torch.tensor(boxes).float(),
            iou_preds=iou_preds,
            points=points_tensor,
            stability_scores=stability_scores,
        )

        return data